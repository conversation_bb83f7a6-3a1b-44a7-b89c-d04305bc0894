#!/usr/bin/env python3
"""
WNN-MRNN多数据集轮次训练脚本

该脚本基于当前的train.py文件，自动轮次训练多个数据集。
支持的数据集: RML, RML2018.01a, HisarMod, TorchSig1024, TorchSig2048, TorchSig4096

使用方法:
    python run_all_datasets.py                                    # 训练默认数据集
    python run_all_datasets.py --datasets rml rml201801a hisar   # 只训练指定数据集
    python run_all_datasets.py --config custom_config.yaml       # 使用自定义配置
"""

# ========================================================================
# 🎯 要训练的数据集配置区域 🎯
# ========================================================================
# 在这里直接修改你想要训练的数据集列表
# 支持的数据集: 'rml', 'rml201801a', 'hisar', 'torchsig1024', 'torchsig2048', 'torchsig4096'
# 注释掉不需要训练的数据集，或者直接删除对应行
DEFAULT_DATASETS_TO_TRAIN = [
    #'rml',           # RML数据集
   # 'rml201801a',    # RML2018.01a数据集
  #  'hisar',         # Hisar数据集
    'torchsig1024',  # TorchSig1024数据集
    'torchsig2048',  # TorchSig2048数据集
    'torchsig4096'   # TorchSig4096数据
]
# ========================================================================

# ========================================================================
# 注意：批处理大小和其他参数现在完全由config.yaml中的数据集特定参数控制
# 不再需要硬编码的DATASET_BATCH_SIZES配置

import os
import subprocess
import time
import yaml
import argparse
import sys
from datetime import datetime

def load_config(config_path):
    """加载YAML配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        try:
            config = yaml.safe_load(f)
            return config
        except yaml.YAMLError as e:
            print(f"配置文件加载错误: {e}")
            sys.exit(1)

def update_config_for_dataset(config, dataset_type):
    """更新配置文件以适应特定数据集"""
    # 深拷贝配置以避免修改原始配置
    import copy
    updated_config = copy.deepcopy(config)
    
    # 更新数据集类型
    updated_config['data']['dataset_type'] = dataset_type
    
    # 设置数据集特定的序列长度
    sequence_lengths = updated_config['data'].get('sequence_lengths', {})
    if dataset_type in sequence_lengths:
        updated_config['model']['sequence_length'] = sequence_lengths[dataset_type]
        print(f"为{dataset_type}设置序列长度: {sequence_lengths[dataset_type]}")
    
    # 设置数据集特定的类别数量（会在train.py中自动调整）
    dataset_classes = {
        'rml': 11,          # RML数据集: 11种调制类型
        'rml201801a': 24,   # RML2018.01a数据集: 24种调制类型
        'hisar': 26,        # Hisar数据集: 26种调制类型
        'torchsig1024': 25, # TorchSig数据集: 25种调制类型
        'torchsig2048': 25,
        'torchsig4096': 25
    }
    
    if dataset_type in dataset_classes:
        updated_config['model']['num_classes'] = dataset_classes[dataset_type]
        print(f"为{dataset_type}设置类别数量: {dataset_classes[dataset_type]}")

    # 🔥 数据集特定参数完全由config.yaml控制 🔥
    # 所有参数（包括batch_size, dropout等）都从数据集特定配置中读取
    if 'dataset_specific_params' in updated_config['model']:
        dataset_params = updated_config['model']['dataset_specific_params'].get(dataset_type, {})
        if 'batch_size' in dataset_params:
            print(f"🔥 为{dataset_type}使用配置文件中的批处理大小: {dataset_params['batch_size']} 🔥")
        else:
            print(f"⚠️ {dataset_type}未配置特定批处理大小，将使用通用配置")

    # 设置数据集特定的模型参数（如果配置中有的话）
    if 'dataset_specific_params' in updated_config['model']:
        dataset_params = updated_config['model']['dataset_specific_params'].get(dataset_type, {})
        if dataset_params:
            print(f"为{dataset_type}应用特定参数: {dataset_params}")
            # 显示具体的参数值
            if 'wavelet_dim' in dataset_params:
                print(f"  - 小波维度: {dataset_params['wavelet_dim']}")
            if 'rnn_dim' in dataset_params:
                print(f"  - RNN维度: {dataset_params['rnn_dim']}")
            if 'num_layers' in dataset_params:
                print(f"  - MMRNN层数: {dataset_params['num_layers']}")
            if 'num_levels' in dataset_params:
                print(f"  - 小波分解层数: {dataset_params['num_levels']}")
            if 'dropout' in dataset_params:
                print(f"  - Dropout率: {dataset_params['dropout']}")
            if 'batch_size' in dataset_params:
                print(f"  - 批处理大小: {dataset_params['batch_size']}")

    # 输出目录会在train.py中自动创建，包含数据集名称和时间戳
    print(f"输出目录将自动创建，格式: {dataset_type}_YYYYMMDD_HHMMSS")
    
    return updated_config

def save_temp_config(config, temp_config_path):
    """将临时配置保存到文件"""
    # 创建临时配置目录
    dirname = os.path.dirname(temp_config_path)
    if dirname:
        os.makedirs(dirname, exist_ok=True)
    
    # 保存配置
    with open(temp_config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    print(f"临时配置已保存到: {temp_config_path}")

def train_single_dataset(dataset_type, config_path):
    """训练单个数据集"""
    print(f"\n{'='*60}")
    print(f"开始训练数据集: {dataset_type.upper()}")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}\n")
    
    # 构建训练命令
    cmd = [sys.executable, 'train.py', '--config', config_path]
    
    print(f"执行命令: {' '.join(cmd)}")
    print(f"工作目录: {os.getcwd()}")
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 执行训练
        process = subprocess.Popen(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.STDOUT, 
            text=True, 
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时显示输出
        for line in process.stdout:
            print(line, end='')
        
        # 等待进程完成
        process.wait()
        
        # 计算训练时间
        end_time = time.time()
        training_time = end_time - start_time
        
        if process.returncode == 0:
            print(f"\n{'='*60}")
            print(f"数据集 {dataset_type.upper()} 训练成功完成")
            print(f"训练时间: {training_time/60:.1f} 分钟 ({training_time:.1f} 秒)")
            print(f"{'='*60}\n")
            return True, training_time
        else:
            print(f"\n{'='*60}")
            print(f"数据集 {dataset_type.upper()} 训练失败")
            print(f"返回码: {process.returncode}")
            print(f"训练时间: {training_time/60:.1f} 分钟")
            print(f"{'='*60}\n")
            return False, training_time
            
    except Exception as e:
        end_time = time.time()
        training_time = end_time - start_time
        print(f"\n训练过程中发生异常: {e}")
        print(f"训练时间: {training_time/60:.1f} 分钟")
        return False, training_time

def main():
    parser = argparse.ArgumentParser(description='WNN-MRNN多数据集轮次训练脚本')
    parser.add_argument('--config', type=str, default='config.yaml',
                        help='基础配置文件路径 (默认: config.yaml)')
    parser.add_argument('--datasets', type=str, nargs='+',
                        default=DEFAULT_DATASETS_TO_TRAIN,
                        help='要训练的数据集列表 (默认: 使用文件顶部配置的数据集)')
    parser.add_argument('--temp_config_dir', type=str, default='temp_configs',
                        help='临时配置文件目录 (默认: temp_configs)')
    args = parser.parse_args()
    
    # 检查基础配置文件是否存在
    if not os.path.exists(args.config):
        print(f"错误: 配置文件不存在: {args.config}")
        sys.exit(1)
    
    # 检查train.py是否存在
    if not os.path.exists('train.py'):
        print("错误: train.py文件不存在，请确保在正确的目录中运行此脚本")
        sys.exit(1)
    
    # 加载基础配置
    print(f"加载基础配置文件: {args.config}")
    base_config = load_config(args.config)
    
    # 支持的数据集列表
    supported_datasets = ['rml', 'rml201801a', 'hisar', 'torchsig1024', 'torchsig2048', 'torchsig4096']
    
    # 验证数据集参数
    datasets_to_train = []
    for dataset in args.datasets:
        if dataset in supported_datasets:
            datasets_to_train.append(dataset)
        else:
            print(f"警告: 不支持的数据集类型 '{dataset}'，跳过")
            print(f"支持的数据集: {', '.join(supported_datasets)}")
    
    if not datasets_to_train:
        print("错误: 没有有效的数据集可训练")
        sys.exit(1)
    
    # 创建临时配置目录
    os.makedirs(args.temp_config_dir, exist_ok=True)
    
    # 训练结果统计
    successful_datasets = []
    failed_datasets = []
    training_times = {}
    total_start_time = time.time()
    
    print(f"\n{'='*60}")
    print(f"WNN-MRNN 多数据集训练开始")
    print(f"总共要训练的数据集: {len(datasets_to_train)}")
    print(f"数据集列表: {', '.join(datasets_to_train)}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    
    # 顺序训练每个数据集
    for i, dataset_type in enumerate(datasets_to_train, 1):
        print(f"\n进度: [{i}/{len(datasets_to_train)}] 准备训练数据集: {dataset_type}")
        
        # 更新配置文件
        dataset_config = update_config_for_dataset(base_config, dataset_type)
        
        # 保存临时配置
        temp_config_path = os.path.join(args.temp_config_dir, f'config_{dataset_type}.yaml')
        save_temp_config(dataset_config, temp_config_path)
        
        # 训练当前数据集
        success, training_time = train_single_dataset(dataset_type, temp_config_path)
        training_times[dataset_type] = training_time
        
        if success:
            successful_datasets.append(dataset_type)
        else:
            failed_datasets.append(dataset_type)
            print(f"数据集 {dataset_type} 训练失败，继续下一个...")
    
    # 计算总训练时间
    total_end_time = time.time()
    total_training_time = total_end_time - total_start_time
    
    # 打印训练结果摘要
    print(f"\n{'='*60}")
    print(f"WNN-MRNN 多数据集训练完成")
    print(f"{'='*60}")
    print(f"总训练时间: {total_training_time/3600:.1f} 小时 ({total_training_time/60:.1f} 分钟)")
    print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print(f"\n成功训练的数据集 ({len(successful_datasets)}/{len(datasets_to_train)}):")
    for ds in successful_datasets:
        time_str = f"{training_times[ds]/60:.1f} 分钟"
        print(f"  ✓ {ds.upper()} - {time_str}")
    
    if failed_datasets:
        print(f"\n失败的数据集 ({len(failed_datasets)}/{len(datasets_to_train)}):")
        for ds in failed_datasets:
            time_str = f"{training_times[ds]/60:.1f} 分钟"
            print(f"  ✗ {ds.upper()} - {time_str}")
    
    # 清理临时配置文件
    print(f"\n清理临时配置文件...")
    for dataset_type in datasets_to_train:
        temp_config_path = os.path.join(args.temp_config_dir, f'config_{dataset_type}.yaml')
        if os.path.exists(temp_config_path):
            os.remove(temp_config_path)
            print(f"  删除: {temp_config_path}")
    
    # 如果临时目录为空，删除它
    try:
        os.rmdir(args.temp_config_dir)
        print(f"  删除临时目录: {args.temp_config_dir}")
    except OSError:
        pass  # 目录不为空或不存在
    
    print(f"\n{'='*60}")
    if len(successful_datasets) == len(datasets_to_train):
        print("🎉 所有数据集训练成功完成！")
    elif successful_datasets:
        print(f"⚠️  部分数据集训练完成 ({len(successful_datasets)}/{len(datasets_to_train)})")
    else:
        print("❌ 所有数据集训练失败")
    print(f"{'='*60}")

if __name__ == '__main__':
    main()
