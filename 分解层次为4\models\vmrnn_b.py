import torch  # 导入PyTorch库，这是一个用于深度学习的Python库
import torch.nn as nn  # 导入神经网络模块，包含了各种神经网络层的定义
import torch.nn.functional as F  # 导入函数式接口，提供了各种激活函数和其他操作
from functools import partial  # 导入partial函数，用于创建部分应用的函数
from .vmamba import MSSBlock  # 从本地模块导入MSSBlock类，这是Mamba状态空间块的基础实现

"""
【MSB继承关系解释】
当MSB类继承自MSSBlock（通过 class MSB(MSSBlock) 语法）时，MSB类会继承MSSBlock的所有属性和方法。
这意味着：
1. MSB可以使用MSSBlock中__init__方法初始化的所有属性，如self.output_proj等
2. 由于MSB实现了自己的forward方法，会覆盖父类MSSBlock的forward方法
3. MSB的forward方法仍然可以使用MSSBlock中定义的属性，如self.self_attention
4. 当创建MSB实例时，会首先调用super().__init__(...)来初始化父类的所有属性

简而言之，继承允许MSB在保留MSSBlock核心功能的同时，添加处理隐藏状态的新能力。
"""

class MSB(MSSBlock):
    """
    Mamba状态空间块，扩展自MSSBlock，添加了处理隐藏状态的能力
    
    这个模块将当前输入和隐藏状态结合起来，通过Mamba机制处理，并输出新的特征表示。
    可以看作是RNN与Mamba的结合体，既有序列建模能力又有状态记忆功能。
    
    参数:
        hidden_dim: 隐藏层维度，表示特征向量的大小
        depth: 保留参数，实际不使用
        drop_path: 随机路径丢弃率，用于正则化，防止过拟合
        norm_layer: 归一化层类型，默认为LayerNorm，用于稳定训练
        d_state: 状态空间维度，影响Mamba内部状态的复杂度
        d_conv: 卷积核大小，影响感受野的大小
        expand: 扩展因子，用于内部特征扩展
        **kwargs: 其他参数，可以传递给父
    """
    def __init__(
        self,
        hidden_dim: int = 0,  # 隐藏层维度，表示特征向量的大小
        depth: int = 1,  # 保留参数，实际不使用
        drop_path: float = 0,  # 随机路径丢弃率，值越大丢弃越多，用于防止过拟合
        norm_layer: nn.Module = partial(nn.LayerNorm, eps=1e-6),  # 归一化层类型，使用eps=1e-6的LayerNorm
        d_state: int = 16,  # 状态空间维度，影响Mamba内部状态的复杂度
        d_conv: int = 4,  # 卷积核大小，影响感受野的大小
        expand: int = 2,  # 扩展因子，用于内部特征扩展，值越大网络容量越大
        **kwargs  # 其他可能的参数
    ):
        # 移除depth参数，确保不传递给父类MSSBlock
        # 这里需要移除depth参数，因为Mamba不接受这个参数
        kwargs_copy = kwargs.copy()
        if 'depth' in kwargs_copy:
            del kwargs_copy['depth']
            
        super().__init__(  # 调用父类MSSBlock的初始化方法
            hidden_dim=hidden_dim,  # 设置隐藏层维度，传递给父类
            drop_path=drop_path,  # 设置随机路径丢弃率
            norm_layer=norm_layer,  # 设置归一化层类型
            d_state=d_state,  # 设置状态空间维度
            d_conv=d_conv,  # 设置卷积核大小
            expand=expand,  # 设置扩展因子
            **kwargs_copy  # 传递其他参数给父类，但不包含depth
        )
        
        # 保存depth参数供MSB使用
        self.depth = depth
        # 创建线性层，用于处理拼接后的特征
        # 输入维度是hidden_dim*2（当前输入和隐藏状态拼接），输出维度是hidden_dim
        self.linear = nn.Linear(hidden_dim * 2, hidden_dim)  
        
        # 保存维度参数，方便后续使用
        self.hidden_dim = hidden_dim  # 存储隐藏层维度

    def forward(self, x, hx=None):
        """
        前向传播函数 - 处理输入特征和隐藏状态，输出新的特征表示
        
        这个函数执行以下步骤：
        1. 对输入和隐藏状态进行归一化和融合
        2. 通过Mamba处理序列
        3. 应用残差连接得到最终输出
        
        参数:
            x: 输入特征，形状为[B, T, C]，B是批次大小，T是时间步数，C是通道数/特征维度
            hx: 隐藏状态，形状与x相同[B, T, C]，默认为None（第一次调用时没有隐藏状态）
            
        返回:
            处理后的特征，形状为[B, T, C]
        """
        B, T, C = x.shape  # 获取输入特征的批次大小(B)、时间步数(T)和通道数(C)
        
        shortcut = x  # 保存输入特征作为残差连接的快捷路径
        x = self.ln_1(x)  # 对输入特征进行层归一化

        if hx is not None:  # 如果提供了隐藏状态
            hx = self.ln_1(hx)  # 对隐藏状态进行层归一化
            # 在通道维度(dim=-1)上拼接当前输入和隐藏状态
            x = torch.cat((x, hx), dim=-1)  
            # 通过线性层将拼接后的特征映射回原始维度
            x = self.linear(x)  

        # 直接通过Mamba模块处理序列，无需调整维度
        mamba_out = self.self_attention(x)
        
        # 应用dropout进行正则化
        x = self.dropout(mamba_out)
        
        # 应用残差连接，将处理后的特征与原始输入特征相加
        x = shortcut + x

        return x


class MMRNNCell(nn.Module):
    """
    Mamba RNN单元 (MMRNNCell) - 结合了Mamba状态空间块(MSB)和RNN机制

    这个类类似于LSTM单元，但使用了Mamba状态空间模型来处理序列信息。
    它维护两个状态：隐藏状态(h)和单元状态(c)，处理输入序列并更新状态。
    """
    def __init__(self,
                 hidden_dim,  # 隐藏层维度，特征向量的大小
                 msb_depth=1,  # MSB块的数量，即网络深度，替代原来的depth参数
                 drop=0.,  # 普通dropout率
                 drop_path=0.,  # 随机路径丢弃率
                 norm_layer=nn.LayerNorm,  # 归一化层类型
                 d_state: int = 16,  # 状态空间维度，影响Mamba内部状态的复杂度
                 d_conv: int = 4,  # 卷积核大小，影响时间序列上的感受野大小
                 expand: int = 2,  # 扩展因子，控制内部网络宽度
                 **kwargs):  # 其他可能的参数
        """
        初始化方法 - 创建MMRNNCell所需的所有组件
        """
        super(MMRNNCell, self).__init__()  # 调用父类nn.Module的初始化方法

        # 创建多个MSB块组成的模块列表
        # 每个MSB块都是一个Mamba状态空间块，能处理序列数据并利用隐藏状态
        self.MSBs = nn.ModuleList([
            MSB(hidden_dim=hidden_dim,  # 设置隐藏层维度
                # 如果drop_path是列表，则取对应索引的值，否则直接使用drop_path
                drop_path=drop_path[i] if isinstance(drop_path, list) else drop_path,
                norm_layer=norm_layer,  # 设置归一化层类型
                d_state=d_state,  # 设置状态空间维度
                d_conv=d_conv,  # 设置卷积核大小
                expand=expand,  # 设置扩展因子
                **kwargs)  # 传递其他参数给父类，但不包含depth
            for i in range(msb_depth)  # 创建msb_depth个MSB块
        ])

        # 隐藏状态自适应投影器字典
        # 用于处理不同序列长度间的隐藏状态传递
        self.hidden_state_adapters = nn.ModuleDict()

        # 存储隐藏层维度，用于动态创建适配器
        self.hidden_dim = hidden_dim

    def _ensure_adapters_on_device(self, device):
        """
        确保所有适配器都在指定设备上

        Args:
            device: 目标设备
        """
        for adapter_key, adapter in self.hidden_state_adapters.items():
            if adapter.weight.device != device:
                self.hidden_state_adapters[adapter_key] = adapter.to(device)

    def _get_or_create_adapter(self, source_length, target_length, device=None):
        """
        获取或创建隐藏状态适配器

        Args:
            source_length: 源序列长度
            target_length: 目标序列长度
            device: 设备信息，确保适配器在正确的设备上

        Returns:
            adapter: 线性投影层，用于序列长度适配
        """
        adapter_key = f"{source_length}_to_{target_length}"

        if adapter_key not in self.hidden_state_adapters:
            # 动态创建适配器：在特征维度上进行线性投影
            # 转置后的维度：[B, C, S] -> [B, C, S']
            adapter = nn.Linear(source_length, target_length)

            # 🔥 关键修复：确保适配器在正确的设备上
            if device is not None:
                adapter = adapter.to(device)
            else:
                # 如果没有指定设备，尝试从模型参数中获取设备信息
                try:
                    model_device = next(self.parameters()).device
                    adapter = adapter.to(model_device)
                except StopIteration:
                    # 如果模型没有参数，保持在CPU上
                    pass

            self.hidden_state_adapters[adapter_key] = adapter

        return self.hidden_state_adapters[adapter_key]

    def _adapt_hidden_state(self, hidden_state, target_length):
        """
        自适应调整隐藏状态的序列长度

        Args:
            hidden_state: 输入隐藏状态 [B, S_source, C]
            target_length: 目标序列长度

        Returns:
            adapted_state: 调整后的隐藏状态 [B, S_target, C]
        """
        if hidden_state is None:
            return None

        B, S_source, C = hidden_state.shape

        if S_source == target_length:
            return hidden_state

        # 获取或创建适配器，传递设备信息
        adapter = self._get_or_create_adapter(S_source, target_length, device=hidden_state.device)

        # 转置 -> 投影 -> 转置回来
        # [B, S_source, C] -> [B, C, S_source] -> [B, C, S_target] -> [B, S_target, C]
        hidden_state = hidden_state.transpose(1, 2)  # [B, S, C] -> [B, C, S]
        hidden_state = adapter(hidden_state)         # [B, C, S_source] -> [B, C, S_target]
        hidden_state = hidden_state.transpose(1, 2)  # [B, C, S] -> [B, S, C]

        return hidden_state

    def forward(self, xt, hidden_states):
        """
        前向传播函数 - 处理输入序列和隐藏状态，返回输出序列和更新后的隐藏状态

        这个函数模拟了类似LSTM的工作流程：
        1. 通过MSB层处理输入和隐藏状态
        2. 计算门控系数Ft
        3. 更新单元状态Ct
        4. 计算新的隐藏状态Ht

        参数:
            xt: 输入序列，形状为[B, S, C]，B是批次大小，S是序列长度，C是特征维度
            hidden_states: 上一分量的隐藏状态，形式为(hx, cx)或None
                           hx和cx的形状都是[B, S, C]
                           第一次调用时可以是None，会自动初始化为全0

        返回:
            Ht: 当前分量的输出，形状为[B, S, C]
            (Ht, Ct): 更新后的隐藏状态元组，可以传递给下一分量使用
        """
        B, S, C = xt.shape  # 获取输入的批次大小(B)、序列长度(S)和通道数(C)

        if hidden_states is None:  # 如果没有提供隐藏状态（初始时刻）
            # 创建全零张量作为初始隐藏状态h，形状为[B, S, C]
            hx = torch.zeros(B, S, C, device=xt.device)
            # 创建全零张量作为初始单元状态c，形状为[B, S, C]
            cx = torch.zeros(B, S, C, device=xt.device)
        else:  # 如果提供了隐藏状态
            hx, cx = hidden_states  # 解包隐藏状态为hx和cx

            # 自适应调整隐藏状态的序列长度以匹配当前输入
            hx = self._adapt_hidden_state(hx, S)
            cx = self._adapt_hidden_state(cx, S)

        outputs = []  # 创建输出列表，用于存储各层的输出
        for index, layer in enumerate(self.MSBs):  # 遍历每个MSB层
            if index == 0:  # 如果是第一层
                # 使用输入xt和隐藏状态hx通过MSB层
                # xt和hx的形状都是[B, S, C]，输出x的形状也是[B, S, C]
                x = layer(xt, hx)
                outputs.append(x)  # 添加到输出列表
            else:  # 如果是后续层
                # 使用前一层的输出作为输入，不使用隐藏状态
                # outputs[-1]形状为[B, S, C]，输出x的形状也是[B, S, C]
                x = layer(outputs[-1], None)
                outputs.append(x)  # 添加到输出列表

        o_t = outputs[-1]  # 获取最后一层的输出，形状为[B, S, C]

        # 计算门控系数，使用sigmoid激活函数，将值压缩到(0,1)范围
        # 公式：Ft = σ(o_t)，其中σ表示sigmoid函数
        # 形状不变：[B, S, C] -> [B, S, C]
        Ft = torch.sigmoid(o_t)

        # 计算候选单元状态，使用tanh激活函数，将值压缩到(-1,1)范围
        # 公式：cell = tanh(o_t)，其中tanh是双曲正切函数
        # 形状不变：[B, S, C] -> [B, S, C]
        cell = torch.tanh(o_t)

        # 更新单元状态，⊙表示元素乘法（Hadamard积）
        # 公式：Ct = Ft ⊙ (cx + cell)
        # cx和cell的形状都是[B, S, C]，加法后形状不变
        # Ft和(cx+cell)做元素乘法，得到Ct，形状仍为[B, S, C]
        Ct = Ft * (cx + cell)

        # 计算新的隐藏状态，⊙表示元素乘法
        # 公式：Ht = Ft ⊙ tanh(Ct)
        # Ct经过tanh后形状不变，仍为[B, S, C]
        # Ft和tanh(Ct)做元素乘法，得到Ht，形状仍为[B, S, C]
        Ht = Ft * torch.tanh(Ct)

        # 返回当前输出Ht和新的隐藏状态元组(Ht, Ct)
        # Ht形状为[B, S, C]，可以作为当前分量的输出
        # (Ht, Ct)是一个元组，可以传递给下一个分量使用
        return Ht, (Ht, Ct)


class MMRNNClassifier(nn.Module):
    """基于MMRNNCell的分类网络 - 用于序列分类任务

    这个网络像是一个RNN分类器，但内部使用了MMRNNCell替代传统RNN/LSTM单元。
    修改后的版本将按频率分量处理序列，而非时间步。

    工作流程:
    1. 将输入特征投影到隐藏维度
    2. 逐频率分量通过多层MMRNNCell处理序列（高频1→高频2→高频3→低频）
    3. 取最后一个分量的输出
    4. 通过分类头(分类器)得到最终预测
    """
    def __init__(
        self,
        input_dim: int,  # 输入特征维度，原始数据的特征大小
        hidden_dim: int,  # 隐藏层维度，模型内部表示的大小
        num_components: int = 4,  # 频率分量数量，等于num_levels+1
        num_layers: int = 2,  # MMRNNCell的层数
        msb_depth: int = 1,  # 每个MSB块内部的深度
        num_classes: int = 10,  # 分类类别数
        d_state: int = 16,  # 状态空间维度
        d_conv: int = 4,  # 卷积核大小
        expand: int = 2,  # 扩展因子
        dropout: float = 0.1  # dropout比率
    ):
        """
        初始化方法 - 创建MMRNNClassifier所需的所有组件
        """
        super().__init__()  # 调用父类nn.Module的初始化方法
        self.hidden_dim = hidden_dim  # 存储隐藏层维度
        self.num_layers = num_layers  # 存储层数
        self.num_components = num_components  # 存储分量数量

        # 输入特征投影层 - 将原始特征映射到隐藏维度
        # 这一层将每个分量的特征从input_dim维映射到hidden_dim维
        self.input_proj = nn.Linear(input_dim, hidden_dim)

        # 创建多层MMRNNCell - 每层MMRNNCell处理序列的一部分
        self.rnn_layers = nn.ModuleList([
            MMRNNCell(
                hidden_dim=hidden_dim,  # 设置隐藏层维度
                msb_depth=msb_depth,  # 使用msb_depth参数控制每个MMRNNCell中MSB块的数量
                drop_path=dropout,  # 使用dropout作为drop_path参数
                d_state=d_state,  # 设置状态空间维度
                d_conv=d_conv,  # 设置卷积核大小
                expand=expand  # 设置扩展因子
            )
            for _ in range(num_layers)  # 创建num_layers个MMRNNCell
        ])

        # 分类头 - 将最终隐藏状态转换为类别预测
        # 使用Sequential容器组合多个层
        self.classifier = nn.Sequential(
            nn.LayerNorm(hidden_dim),  # 先对特征进行归一化
            nn.Linear(hidden_dim, num_classes)  # 然后映射到类别数量大小的输出
        )

    def forward(self, components_list, component_lengths=None):
        """
        前向传播函数 - 处理分量序列并输出分类预测

        工作流程:
        1. 将各分量的特征投影到隐藏维度
        2. 初始化所有层的隐藏状态为None
        3. 按照分量顺序处理序列（先处理所有高频分量，再处理低频分量）
        4. 取最后一个分量(低频)的最终层输出
        5. 将特征展平并通过分类头得到类别预测

        参数:
            components_list: 分量列表，每个分量形状为[B, S_i, W]，其中S_i是各分量的序列长度
            component_lengths: 各分量的序列长度列表（可选，用于验证）

        返回:
            logits: 分类预测结果，形状为[B, num_classes]
        """
        # 兼容旧的调用方式
        if isinstance(components_list, torch.Tensor):
            # 如果传入的是张量，按照旧的方式处理
            return self._forward_legacy(components_list)

        # 新的分量列表处理方式
        outputs = []  # 存储每个分量处理后的输出

        # 所有层的隐藏状态初始化为None
        layer_hidden_states = [None] * self.num_layers

        # 逐分量处理序列（高频1→高频2→...→高频n→低频）
        for c, component in enumerate(components_list):
            # component形状为[B, S_c, W]，其中S_c是当前分量的序列长度
            B, S_c, W = component.shape

            # 将当前分量的特征投影到隐藏维度
            # [B, S_c, W] -> [B, S_c, hidden_dim]
            component_proj = self.input_proj(component)

            # 通过每一层MMRNNCell处理当前分量
            for layer_idx, layer in enumerate(self.rnn_layers):
                # 获取上一层的输出作为当前层的输入
                if layer_idx == 0:  # 如果是第一层
                    layer_input = component_proj  # 使用当前分量的投影输入
                else:  # 如果是后续层
                    layer_input = out  # 使用前一层的输出

                # 通过当前层的MMRNNCell处理（内部会自动适配隐藏状态维度）
                out, new_hidden = layer(layer_input, layer_hidden_states[layer_idx])

                # 更新该层的隐藏状态，用于下一个分量
                layer_hidden_states[layer_idx] = new_hidden

            # 存储最后一层在当前分量的输出
            outputs.append(out)

        # 取最后一个分量(低频)的输出进行分类
        # out形状为[B, S_last, hidden_dim]
        last_output = outputs[-1]

        # 将特征在序列维度上平均池化
        # [B, S_last, hidden_dim] -> [B, hidden_dim]
        pooled_output = torch.mean(last_output, dim=1)

        # 分类预测 - 通过分类头得到最终预测
        # [B, hidden_dim] -> [B, num_classes]
        logits = self.classifier(pooled_output)

        return logits

    def _forward_legacy(self, x):
        """
        兼容旧版本的前向传播方法

        参数:
            x: 输入序列，形状为[B, C*S, W]

        返回:
            logits: 分类预测结果，形状为[B, num_classes]
        """
        B, CS, W = x.shape  # 获取输入的批次大小(B)和总序列长度(CS = C*S)及特征维度(W)

        # 先用输入特征投影层处理
        # [B, CS, W] -> [B, CS, hidden_dim]
        x_proj = self.input_proj(x)

        # 使用初始化时传入的num_components
        num_components = self.num_components

        # 为了向后兼容，仍然检查是否有明确指定的num_components属性
        component_hint = getattr(x, 'num_components', None)
        if component_hint is not None:
            num_components = component_hint

        # 计算每个分量的序列长度 = 总序列长度 / 分量数量
        S = CS // num_components

        # 重塑输入为分量和序列维度分离的形式
        # [B, CS, hidden_dim] -> [B, C, S, hidden_dim]
        x = x_proj.reshape(B, num_components, S, self.hidden_dim)

        # 所有层的隐藏状态初始化为None
        layer_hidden_states = [None] * self.num_layers

        outputs = []  # 存储每个分量处理后的输出

        # 逐分量处理序列（高频1→高频2→...→高频n→低频）
        for c in range(num_components):
            # 提取当前分量
            # [B, C, S, hidden_dim] -> [B, S, hidden_dim]
            component = x[:, c]

            # 通过每一层MMRNNCell处理当前分量
            for layer_idx, layer in enumerate(self.rnn_layers):
                # 获取上一层的输出作为当前层的输入
                if layer_idx == 0:  # 如果是第一层
                    layer_input = component  # 使用当前分量的输入
                else:  # 如果是后续层
                    layer_input = out  # 使用前一层的输出

                # 通过当前层的MMRNNCell处理
                out, new_hidden = layer(layer_input, layer_hidden_states[layer_idx])

                # 更新该层的隐藏状态，用于下一个分量
                layer_hidden_states[layer_idx] = new_hidden

            # 存储最后一层在当前分量的输出
            outputs.append(out)

        # 取最后一个分量(低频)的输出进行分类
        # out形状为[B, S, hidden_dim]
        last_output = outputs[-1]

        # 将特征在序列维度上平均池化
        # [B, S, hidden_dim] -> [B, hidden_dim]
        pooled_output = torch.mean(last_output, dim=1)

        # 分类预测 - 通过分类头得到最终预测
        # [B, hidden_dim] -> [B, num_classes]
        logits = self.classifier(pooled_output)

        return logits
